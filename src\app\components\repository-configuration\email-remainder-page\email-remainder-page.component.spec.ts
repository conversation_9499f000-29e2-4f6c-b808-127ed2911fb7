import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { EmailRemainderPageComponent } from './email-remainder-page.component';
import { ReactiveFormsModule, FormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { Component, Input, Output, EventEmitter, Directive } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Mocks
class MockActivatedRoute {
  params = of({ id: 'new' });
}
class MockRouter {
  navigate = jasmine.createSpy('navigate');
}
class MockToastrService {
  success = jasmine.createSpy('success');
  error = jasmine.createSpy('error');
}
class MockRepositoryConfigService {
  getEmailReminderDefaults = jasmine.createSpy('getEmailReminderDefaults').and.returnValue(of({
    portfolioCompanys: [{ id: 1, name: 'TestCo' }],
    documentTypes: [{ id: 2, name: 'DocType' }],
    toRecipients: [{ email: '<EMAIL>' }],
    ccReciepients: [{ email: '<EMAIL>', internalID: 1 }]
  }));
  updateEmailReminder = jasmine.createSpy('updateEmailReminder').and.returnValue(of({}));
}

// Stub for app-custom-quill-editor
@Component({
  selector: 'app-custom-quill-editor',
  template: '<div></div>',
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: CustomQuillEditorStubComponent,
    multi: true
  }]
})
class CustomQuillEditorStubComponent implements ControlValueAccessor {
  @Input() ngModel: any;
  @Input() formControl: any;
  @Output() ngModelChange = new EventEmitter<any>();
  writeValue(obj: any): void {}
  registerOnChange(fn: any): void {}
  registerOnTouched(fn: any): void {}
  setDisabledState?(isDisabled: boolean): void {}
}

// Kendo stubs
@Component({selector: 'kendo-formfield', template: '<ng-content></ng-content>'})
class KendoFormFieldStubComponent {}
@Directive({selector: '[kendoRadioButton]'})
class KendoRadioButtonStubDirective {
  @Input() formControlName: any;
  @Input() value: any;
}
@Directive({selector: '[kendoButton]'})
class KendoButtonStubDirective {
  @Input() themeColor: any;
  @Input() fillMode: any;
  @Input() class: any;
}

describe('EmailRemainderPageComponent', () => {
  let component: EmailRemainderPageComponent;
  let fixture: ComponentFixture<EmailRemainderPageComponent>;
  let repositoryConfigService: MockRepositoryConfigService;
  let toastrService: MockToastrService;
  let router: MockRouter;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        EmailRemainderPageComponent,
        CustomQuillEditorStubComponent,
        KendoRadioButtonStubDirective,
        KendoButtonStubDirective
      ],
      imports: [
        ReactiveFormsModule,
        FormsModule,
        InputsModule,
        DropDownsModule,
        DateInputsModule,
        ButtonsModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: ActivatedRoute, useClass: MockActivatedRoute },
        { provide: Router, useClass: MockRouter },
        { provide: ToastrService, useClass: MockToastrService },
        { provide: RepositoryConfigService, useClass: MockRepositoryConfigService }
      ]
      // schemas: [NO_ERRORS_SCHEMA] // Remove this line to catch template errors
    }).compileComponents();

    fixture = TestBed.createComponent(EmailRemainderPageComponent);
    component = fixture.componentInstance;
    repositoryConfigService = TestBed.inject(RepositoryConfigService) as any;
    toastrService = TestBed.inject(ToastrService) as any;
    router = TestBed.inject(Router) as any;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.reminderForm).toBeDefined();
    expect(component.reminderForm.get('subject')?.value).toBe('Document Request');
  });

  it('should validate toEmailArray (required)', () => {
    component.toEmailArray = [];
    component.reminderForm.get('to')?.updateValueAndValidity();
    expect(component.reminderForm.get('to')?.errors?.required).toBeTrue();
    component.toEmailArray = ['<EMAIL>'];
    component.reminderForm.get('to')?.updateValueAndValidity();
    expect(component.reminderForm.get('to')?.errors).toBeNull();
  });

  it('should add and remove To email', () => {
    component.reminderForm.get('to')?.setValue('<EMAIL>');
    component.addToEmail({ preventDefault: () => {} });
    expect(component.toEmailArray).toContain('<EMAIL>');
    component.removeToEmail('<EMAIL>');
    expect(component.toEmailArray).not.toContain('<EMAIL>');
  });

  it('should add and remove CC recipient', () => {
    component.reminderForm.get('cc')?.setValue('<EMAIL>');
    component.addCcRecipient({ preventDefault: () => {} });
    expect(component.ccRecipientsArray.some(r => r.email === '<EMAIL>')).toBeTrue();
    component.removeCcRecipient({ email: '<EMAIL>' });
    expect(component.ccRecipientsArray.some(r => r.email === '<EMAIL>')).toBeFalse();
  });

  it('should not add invalid To/CC emails', () => {
    component.reminderForm.get('to')?.setValue('invalid');
    component.addToEmail({ preventDefault: () => {} });
    expect(component.toEmailArray).not.toContain('invalid');
    component.reminderForm.get('cc')?.setValue('invalid');
    component.addCcRecipient({ preventDefault: () => {} });
    expect(component.ccRecipientsArray.some(r => r.email === 'invalid')).toBeFalse();
  });

  it('should update editor placeholder and message', () => {
    component.selectedPortfolioCompanies = [{ name: 'TestCo' }];
    component.selectedDocumentTypes = [{ name: 'DocType' }];
    // Indirectly trigger updateEditorPlaceholder via public method
    (component as any).updateEditorPlaceholder(); // Use bracket notation to bypass TS for test only
    expect(component.message).toContain('TestCo');
    expect(component.message).toContain('DocType');
  });

  it('should show error if form is invalid on submit', () => {
    component.toEmailArray = [];
    component.reminderForm.get('subject')?.setValue('');
    component.onSubmit();
    expect(toastrService.error).toHaveBeenCalled();
  });

  it('should navigate on cancel', () => {
    component.onCancel();
    expect(router.navigate).toHaveBeenCalledWith(['/repository-configuration'], jasmine.objectContaining({ queryParams: { tab: 'Email Notification' } }));
  });

  it('should reset the form on onReset', () => {
    component.reminderForm.get('subject')?.setValue('Changed');
    component.onReset();
    expect(component.reminderForm.get('subject')?.value).toBe('Document Request');
  });

  it('should mark all controls as touched via onSubmit when invalid', () => {
    const controls = component.reminderForm.controls;
    Object.values(controls).forEach(ctrl => spyOn(ctrl, 'markAsTouched'));
    component.toEmailArray = [];
    component.reminderForm.get('subject')?.setValue('');
    component.onSubmit();
    Object.values(controls).forEach(ctrl => {
      expect(ctrl.markAsTouched).toHaveBeenCalled();
    });
  });

  it('should update reminder validators and set correct validators via onTotalRemindersChange', () => {
    component.reminderForm.get('totalReminders')?.setValue(3);
    component.onTotalRemindersChange();
    // Check that validators are set for reminder2 and reminder3
    expect(component.reminderForm.get('reminder2')?.validator).toBeDefined();
    expect(component.reminderForm.get('reminder3')?.validator).toBeDefined();
  });

  it('should return correct max days for reminders', () => {
    component.reminderForm.get('reminderFrequency')?.setValue('quarterly');
    component.reminderForm.get('reminder2')?.setValue(10);
    expect(component.getMaxDays(3)).toBe(80); // 90 - 10
  });

  it('should return true/false for shouldShowReminder', () => {
    component.reminderForm.get('totalReminders')?.setValue(2);
    expect(component.shouldShowReminder(1)).toBeTrue();
    expect(component.shouldShowReminder(3)).toBeFalse();
  });

  it('should return correct reminder label', () => {
    expect(component.getReminderLabel(1)).toBe('1st Reminder');
    expect(component.getReminderLabel(4)).toBe('4th Reminder');
  });

  it('should clear cached options and update validators on onReminderValueChange', () => {
    component.reminderDayOptionsMap[3] = [{ text: 'test', value: '1' }];
    // updateReminderValidators is private, so just check cache cleared
    component.onReminderValueChange(2);
    expect(component.reminderDayOptionsMap[3]).toBeUndefined();
  });

  it('should generate reminder day options if not cached', () => {
    const options = component.getReminderDayOptions(2);
    expect(Array.isArray(options)).toBeTrue();
    expect(options.length).toBeGreaterThan(0);
  });

  it('should get reminder value', () => {
    component.reminderForm.get('reminder2')?.setValue(5);
    expect(component.getReminderValue(2)).toBe(5);
  });

  it('should validate isFieldInvalid', () => {
    const field = component.reminderForm.get('subject');
    field?.markAsTouched();
    field?.setValue('');
    expect(component.isFieldInvalid('subject')).toBeTrue();
  });

  it('should return correct field error', () => {
    component.toEmailArray = [];
    const toControl = component.reminderForm.get('to');
    toControl?.setErrors({ required: true });
    expect(component.getFieldError('to')).toContain('At least one recipient');
    const subjectControl = component.reminderForm.get('subject');
    subjectControl?.setErrors({ required: true });
    expect(component.getFieldError('subject')).toContain('required');
    const ccControl = component.reminderForm.get('cc');
    ccControl?.setErrors({ email: true });
    expect(component.getFieldError('cc')).toContain('valid email');
  });

  it('should add valid To email and not add duplicates', () => {
    component.reminderForm.get('to')?.setValue('<EMAIL>');
    component.addToEmail({ preventDefault: () => {} });
    expect(component.toEmailArray).toContain('<EMAIL>');
    component.reminderForm.get('to')?.setValue('<EMAIL>');
    component.addToEmail({ preventDefault: () => {} });
    expect(component.toEmailArray.filter(e => e === '<EMAIL>').length).toBe(1);
  });

  it('should not add invalid To email', () => {
    component.reminderForm.get('to')?.setValue('bademail');
    component.addToEmail({ preventDefault: () => {} });
    expect(component.toEmailArray).not.toContain('bademail');
  });

  it('should remove To email', () => {
    component.toEmailArray = ['<EMAIL>'];
    component.removeToEmail('<EMAIL>');
    expect(component.toEmailArray).not.toContain('<EMAIL>');
  });

  it('should add valid CC recipient and not add duplicates', () => {
    component.reminderForm.get('cc')?.setValue('<EMAIL>');
    component.addCcRecipient({ preventDefault: () => {} });
    expect(component.ccRecipientsArray.some(r => r.email === '<EMAIL>')).toBeTrue();
    component.reminderForm.get('cc')?.setValue('<EMAIL>');
    component.addCcRecipient({ preventDefault: () => {} });
    expect(component.ccRecipientsArray.filter(r => r.email === '<EMAIL>').length).toBe(1);
  });

  it('should not add invalid CC recipient', () => {
    component.reminderForm.get('cc')?.setValue('badcc');
    component.addCcRecipient({ preventDefault: () => {} });
    expect(component.ccRecipientsArray.some(r => r.email === 'badcc')).toBeFalse();
  });

  it('should remove CC recipient', () => {
    component.ccRecipientsArray = [{ email: '<EMAIL>' }];
    component.removeCcRecipient({ email: '<EMAIL>' });
    expect(component.ccRecipientsArray.some(r => r.email === '<EMAIL>')).toBeFalse();
  });

  it('should reset reminders and update validators on onReminderFrequencyChange', () => {
    component.reminderForm.get('reminder2')?.setValue(5);
    component.reminderForm.get('reminder3')?.setValue(6);
    component.onReminderFrequencyChange();
    expect(component.reminderForm.get('totalReminders')?.value).toBe(1);
    expect(component.reminderForm.get('reminder2')?.value).toBeNull();
    expect(component.reminderForm.get('reminder3')?.value).toBeNull();
  });

  it('should update message on onMessageChange with string', () => {
    component.onMessageChange('<b>hello</b>');
    expect(component.message).toBe('<b>hello</b>');
    expect(component.reminderForm.get('message')?.value).toBe('<b>hello</b>');
  });

  it('should update message on onMessageChange with object (simulate Quill)', () => {
    (window as any).Quill = class {
      root = { innerHTML: '<p>obj</p>' };
      setContents() {}
      constructor(_: any) {}
    };
    component.onMessageChange({});
    expect(component.message).toBe('<p>obj</p>');
    expect(component.reminderForm.get('message')?.value).toBe('<p>obj</p>');
  });

  it('should populate data for edit flow', () => {
    component.editableData = {
      featureID: 1,
      reminderID: 'rem1',
      subject: 'Subj',
      emailBody: '<b>Body</b>',
      frequencyType: 2,
      totalRemindersPerCycle: 3,
      remainder1Date: new Date(),
      remainder2: '2',
      remainder3: '3',
      remainder4: '4',
      remainder5: '5',
      portfolioCompanys: [],
      documentTypes: [],
      toRecipients: [],
      ccReciepients: []
    };
    component.populateDataforEditFlow();
    expect(component.reminderForm.get('subject')?.value).toBe('Subj');
    expect(component.reminderForm.get('reminder2')?.value).toBe('2');
    expect(component.reminderForm.get('reminder3')?.value).toBe('3');
    expect(component.reminderForm.get('reminder4')?.value).toBe('4');
    expect(component.reminderForm.get('reminder5')?.value).toBe('5');
    expect(component.reminderForm.get('reminderFrequency')?.value).toBe('quarterly');
  });

  it('should call onAttachFile (no-op)', () => {
    expect(() => component.onAttachFile()).not.toThrow();
  });
});
