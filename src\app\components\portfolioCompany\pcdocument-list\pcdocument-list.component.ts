import { Component, ViewChild, ElementRef, OnInit, Input } from '@angular/core';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { ToastrService } from 'ngx-toastr';
import { DatePipe } from '@angular/common';
import { ErrorMessage } from 'src/app/services/miscellaneous.service';
import { PageConfigurationDocumentPageDetails } from 'src/app/common/enums';
import * as moment from "moment";
import { FeaturesEnum } from 'src/app/services/permission.service';

@Component({
  selector: 'app-pcdocument-list',
  templateUrl: './pcdocument-list.component.html',
  styleUrls: ['./pcdocument-list.component.scss'],
  providers: [DatePipe]
})
export class PCDocumentListComponent implements OnInit {
  showOptions = false;
  selectedDocumentType = 'localDocuments';
  isDragging = false;
  uploadedFiles: any[] = [];
  actualUploadedFiles: File[] = []; // Changed to array of File objects
  selectedFolderPath: string = '';
  isAddButtonEnabled: boolean = false;
  isAnyDocumentSelected: boolean = false; // Added property to track document selection
  documents: any[] = [];
  
  // New properties for selection and delete functionality
  showDeletePopup: boolean = false;
  showSelectionPopup: boolean = false;
  selectedDocumentIds: string[] = [];
  selectedDocumentCount: number = 0;
  documentToDelete: any = null;
  isDeleteDisabled: boolean = false;
  isMultipleDelete: boolean = true;
  singleDeleteMessage: string = 'This File will be permanently Deleted from your computer ?';
  multipleDeleteMessage: string = 'All the Selected File will be permanently delete from your system.';
  isLoading: boolean = false;

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @Input() PortfolioCompanyId: string = ''; 
  @Input() canEdit: boolean = false;
  @Input() documentsFieldPageConfig: any;
  @Input() IsFund: boolean = false;
  documentsListAlias:string = 'Documents List';

  constructor(
    private documentService: RepositoryConfigService,
    private toastr: ToastrService,
  ) { }

  ngOnInit() {
    // Removed loadFilesFromStorage() call
    this.documentsListAlias = this.documentsFieldPageConfig?.find((x: any) => x.name == PageConfigurationDocumentPageDetails.DocumentsList)?.displayName;
  }
  setSelectedFolder(data: any) {
    if (data) {
      this.selectedFolderPath = data.folderPath;
      this.isAddButtonEnabled = !data.isFromUnconfig && !!data.folderPath && data.folderPath.trim() !== '';
      this.showOptions = false;
      this.isLoading = true;
      this.loaddocumentstoShow(data.folderPath);
    }

    // Reset selection-related state when changing folders
    this.showSelectionPopup = false;
    this.selectedDocumentCount = 0;
    this.selectedDocumentIds = [];
  }

  checkPermission(): boolean {
    if (!this.canEdit) {
      this.showNoAccessError();
      return false;
    }
    return true;
  }

  showNoAccessError() {
    this.toastr.error(ErrorMessage.NoAccess, "", {
      positionClass: "toast-center-center",
    });
  }

  saveFilesToServer() {
    const formData = new FormData();
    
    // Only upload selected files
    const selectedFileIndexes = this.uploadedFiles
      .map((file, index) => ({ index, selected: file.selected }))
      .filter(item => item.selected)
      .map(item => item.index);
    
    if (selectedFileIndexes.length > 0) {
      for (const index of selectedFileIndexes) {
        if (index < this.actualUploadedFiles.length) {
          formData.append('Files', this.actualUploadedFiles[index]);
        }
      }
      
      // Construct the folder path according to the required format
      formData.append('FolderPath', this.selectedFolderPath);
      formData.append('FeatureID', this.IsFund ? FeaturesEnum.Fund.toString() : FeaturesEnum.PortfolioCompany.toString());
      
      this.documentService.uploadDocumentstoServer(this.PortfolioCompanyId, formData).subscribe(
        (response) => { 
          if (response.isSuccess) {
            this.toastr.success("Files uploaded successfully", "", {
              positionClass: "toast-center-center",
            });
            this.actualUploadedFiles = [];
            this.uploadedFiles =[];
            this.showOptions = false;
            this.isAnyDocumentSelected = false;
            this.loaddocumentstoShow(this.selectedFolderPath);            
            // Reset selection state after successful upload
            
            
          } else {
            this.toastr.error(response.message, "", {
              positionClass: "toast-center-center",
            });          }
        });
    } else {
      this.toastr.warning('Please select at least one file to upload.');
    }
  } 

  showDocumentOptions() {
    if (!this.checkPermission()) {
      return;
    }
    
    if (this.isAddButtonEnabled) {
      this.uploadedFiles = [];
      this.actualUploadedFiles = [];
      this.isAnyDocumentSelected = false;
      this.showOptions = true;
    }
  }
  triggerFileInput() {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: any) {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Properly type cast to File objects using as File[]
      for (const file of Array.from(files) as File[]) {
        this.actualUploadedFiles.push(file);
        this.uploadedFiles.push({
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          selected: false  // Initialize with not selected
        });
      }
    }
  }

  handleBackAction() {
    this.uploadedFiles = [];
    this.actualUploadedFiles = [];
    this.showOptions = false;
    this.isAnyDocumentSelected = false;
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
    
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      // Properly type cast to File objects
      for (const file of Array.from(files) as File[]) {
        this.actualUploadedFiles.push(file);
        this.uploadedFiles.push({
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          selected: false  // Initialize with not selected
        });
      }
    }
  }
  // New method to add documents after browsing
  addDocuments() {
    if (this.uploadedFiles.length > 0) {
      this.saveFilesToServer();
    } else {
      this.toastr.warning('Please select files to upload first.');
    }
  }

  hideDocumentOptions() {
    this.showOptions = false;
    // Clear both document arrays when back button is clicked
    this.uploadedFiles = [];
    this.actualUploadedFiles = [];
    this.isAnyDocumentSelected = false;
  }

  toggleDocumentSelection(dataItem: any) {
    if (dataItem) {
      // Toggle the selected state
      dataItem.selected = !dataItem.selected;
      
      if (this.showOptions) {
        this.isAnyDocumentSelected = this.uploadedFiles.some(file => file.selected);
      } else {
        this.updateSelectionStatus();
      }
    }
  }

  // Method to check if all documents are selected
  areAllDocumentsSelected(): boolean {
    if (this.showOptions) {
      return this.uploadedFiles.length > 0 && this.uploadedFiles.every(file => file.selected);
    } else {
      return this.documents.length > 0 && this.documents.every(doc => doc.selected);
    }
  }

  toggleAllDocumentsSelection(): void {
    const allSelected = this.areAllDocumentsSelected();
    
    if (this.showOptions) {
      // Toggle selection state of all uploaded files
      this.uploadedFiles.forEach(file => {
        file.selected = !allSelected;
      });
      
      // Update the flag to track if any document is selected
      this.isAnyDocumentSelected = !allSelected && this.uploadedFiles.length > 0;
    } else {
      // Toggle selection state of all documents
      this.documents.forEach(doc => {
        doc.selected = !allSelected;
      });
      
      this.updateSelectionStatus();
    }
  }

  // Update selection status and manage selection popup
  updateSelectionStatus(): void {
    this.selectedDocumentCount = this.documents.filter(doc => doc.selected).length;
    
    // Make sure we're capturing the correct property name for document IDs
    // This extracts the correct ID property from your document objects
    this.selectedDocumentIds = this.documents
      .filter(doc => doc.selected)
      .map(doc => doc.documentId);
    
    this.showSelectionPopup = this.selectedDocumentCount > 0;
    
    // Set isMultipleDelete to true when multiple documents are selected
    if (this.selectedDocumentCount > 0) {
      this.isMultipleDelete = true;
    }
    
    // Set isDeleteDisabled property based on some condition if needed
    // For example, you might want to disable delete for certain document types
    this.isDeleteDisabled = false; // Set to true when you need to disable
  }

  // Show delete confirmation popup for a single document
  showDeleteConfirmation(document: any): void {
    if (!this.checkPermission()) {
      return;
    }
    this.documentToDelete = document;
    // Make sure to set selectedDocumentIds as an array with a single element
    this.selectedDocumentIds = [document.documentId];
    this.isMultipleDelete = false; // Set to false for single document deletion
    this.showDeletePopup = true;
  }

  // Delete selected documents
  deleteSelectedDocuments(): void {
    if (this.selectedDocumentIds.length === 0) {
      this.toastr.warning('No documents selected for deletion.');
      return;
    }

    // Create FormData for the delete request
    const formData = new FormData();
    formData.append('Path', this.selectedFolderPath);
    
    // Append each document ID separately with the same key
    this.selectedDocumentIds.forEach(documentId => {
      formData.append('DocumentIds', documentId);
    });
    
    formData.append('ModifiedBy', '0');    

    this.documentService.deleteDocuments(this.PortfolioCompanyId, formData).subscribe(
      (response) => {
        if (response.isSuccess) {
          if(this.isMultipleDelete && this.selectedDocumentIds.length > 1) {
            this.toastr.success("All the selected files have been deleted successfully.", "", {
              positionClass: "toast-center-center",
            });
          }
          else{
            this.toastr.success("Selected file has been deleted successfully.", "", {
              positionClass: "toast-center-center",
            });
          }
          
          // Refresh document list
          this.loaddocumentstoShow(this.selectedFolderPath);
          
          // Reset selection states
          this.closeDeletePopup();
          this.showSelectionPopup = false;
          this.showDeletePopup = false;
          this.selectedDocumentCount = 0;
          this.selectedDocumentIds = [];
        } else {
          this.toastr.error(response.message, "", {
            positionClass: "toast-center-center",
          });
        }
      },
      (error) => {
        console.error('Error deleting documents:', error);
        this.showDeletePopup = false;
        this.toastr.error("Failed to delete documents", "", {
          positionClass: "toast-center-center",
        });
      }
    );
  }

  // Close delete confirmation popup
  closeDeletePopup(): void {
    this.showDeletePopup = false;
    this.documentToDelete = null;

    if(this.isMultipleDelete){
      this.showSelectionPopup = true;
    }
  }

  hideSelectionPopup(): void {
    this.showSelectionPopup = false;    
    this.showDeletePopup = true;
  }

  // Close selection popup
  closeSelectionPopup(): void {
    this.showSelectionPopup = false;    
    // Optionally clear selections if you want to clear when popup is closed
    this.documents.forEach(doc => {
      doc.selected = false;
    });
    
    this.updateSelectionStatus();
  }

  // New method to remove unselected files from the UI
  removeUnselectedFiles() {
    const unselectedIndices = this.uploadedFiles
      .map((file, index) => ({ index, selected: file.selected }))
      .filter(item => !item.selected)
      .map(item => item.index)
      .sort((a, b) => b - a); // Sort in descending order
        for (const index of unselectedIndices) {
      this.uploadedFiles.splice(index, 1);
      this.actualUploadedFiles.splice(index, 1);
    }
  }

  loaddocumentstoShow(path: string) {
    const formData = new FormData();
    formData.append('Path', path);    
    formData.append('FeatureID', this.IsFund ? FeaturesEnum.Fund.toString() : FeaturesEnum.PortfolioCompany.toString());
    this.isLoading = true;
    this.documentService.getDocumentList(this.PortfolioCompanyId , formData).subscribe((response) => {
      this.isLoading = false;
      if (response.isSuccess) {
        this.documents = response.data;
      } else {
        this.toastr.error( response.message);
      }
    },
    (error) => {
      this.isLoading = false;
      this.toastr.error('Error loading documents');
    });
  }

  // Format document name to remove file extension
  formatDocumentName(documentName: string): string {
    if (!documentName) return '';
    
    // Remove file extensions like .pdf, .doc, etc.
    const lastDotIndex = documentName.lastIndexOf('.');
    if (lastDotIndex !== -1) {
      return documentName.substring(0, lastDotIndex);
    }
    return documentName;
  }
  
  handleDeleteSelectedClick(): void {
    if (!this.checkPermission()) {
      return;
    }
    this.hideSelectionPopup();
  }

  // Format date to display in the required format: DD/MM/YYYY HH:MM am/pm
  formatDate(date: string): string {
   if (!date) {
         return "";
       }
   
       try {
         // Parse the date as UTC and convert to local time
         const localDate = moment.utc(date).local();
         const format = "DD/MM/YYYY hh:mm A"; // 12-hour format with AM/PM
         // Format using the specified format string
         return localDate.format(format);
       } catch (error) {
         console.error("Error converting UTC date to local time:", error);
         return "";
       }
  }

  // Download a document by its info
  downloadDocument(document: any): void {
  if (!document || !document.documentId) {
    this.toastr.error('Invalid document for download.');
    return;
  }
  this.documentService.downloadDocument(this.PortfolioCompanyId, document.documentId, this.selectedFolderPath)
    .subscribe({
      next: (response: Blob) => {
        const blob: Blob = response;
        const filename = document.documentName || 'document';
        const url = window.URL.createObjectURL(blob);
        const a = window.document.createElement('a');
        a.href = url;
        a.download = filename;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      error: () => {
        this.toastr.error("Failed to download file", "", {
          positionClass: "toast-center-center",
        });
      }
    });
}
}
