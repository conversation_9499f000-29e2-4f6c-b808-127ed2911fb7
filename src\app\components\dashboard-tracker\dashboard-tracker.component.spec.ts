import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { DashboardTrackerComponent } from './dashboard-tracker.component';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

class MockDashboardTrackerService {
  getDashboardTableData() {
    // Default mock returns data and columns
    return of({
      data: [{ id: 1, name: 'Test' }],
      columns: [{ field: 'id' }, { field: 'name' }]
    });
  }
}

describe('DashboardTrackerComponent', () => {
  let component: DashboardTrackerComponent;
  let fixture: ComponentFixture<DashboardTrackerComponent>;

  let dashboardTrackerService: DashboardTrackerService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DashboardTrackerComponent],
      providers: [
        { provide: DashboardTrackerService, useClass: MockDashboardTrackerService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardTrackerComponent);
    component = fixture.componentInstance;
    dashboardTrackerService = TestBed.inject(DashboardTrackerService);
  });


  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default property values', () => {
    expect(component.passedClass).toBe('');
    expect(component.isDashboardConfigurationTab).toBe(false);
    expect(component.isLoading).toBe(true);
    expect(component.moreColumn).toBe(false);
    expect(component.gridData).toEqual([]);
    expect(component.gridColumns).toEqual([]);
  });


  it('should set gridData, gridColumns, and isLoading on ngOnInit', () => {
    fixture.detectChanges(); // triggers ngOnInit
    expect(component.gridData).toEqual([{ id: 1, name: 'Test' }]);
    expect(component.gridColumns).toEqual([{ field: 'id' }, { field: 'name' }]);
    expect(component.isLoading).toBe(false);
  });

  it('should not set gridData or gridColumns if response is falsy', () => {
    spyOn(dashboardTrackerService, 'getDashboardTableData').and.returnValue(of(null));
    fixture.detectChanges();
    expect(component.gridData).toEqual([]);
    expect(component.gridColumns).toEqual([]);
    expect(component.isLoading).toBe(false);
  });
});
