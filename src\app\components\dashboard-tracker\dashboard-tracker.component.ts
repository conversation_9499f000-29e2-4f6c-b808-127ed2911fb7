import { Component, OnInit, Input } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridData: any[] = [];
  gridColumns: any[] = [];
  constructor(
    private dashboardTrackerService: DashboardTrackerService,
  ) {}
  ngOnInit(): void {
    this.dashboardTrackerService.getDashboardTableData().subscribe((response) => {
      // If API returns { data, columns }, handle accordingly
      if (response && response.data && response.columns) {
        this.gridData = response.data;
        this.gridColumns = response.columns;
      } 
      this.isLoading = false;
    });
  }

  navigateToDashboardConfig(): void {
   
  }
}
