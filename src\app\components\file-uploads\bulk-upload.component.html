<div class="row">
    <div class="col-lg-12">
        <div class="add-user-component">
            <div class="card card-main">
                <div class="moduleContainer">
                    <div class="row ml-0 mr-0">
                        <div class="col-12 plrb20">
                            <div class="d-inline-block pt20">
                                <label class="Caption-M" for="FirstName">Module</label>
                                <div class="pt-1">
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="model.moduleDetails"
                                        #module="ngModel" [fillMode]="'solid'" [filterable]="true" name="module" [virtual]="virtual"
                                        class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                        [data]="masterModel.moduleList" [filterable]="true" [value]="model.moduleDetails" [valuePrimitive]="false"
                                        textField="pageConfigAliasName" placeholder="Select Module"  id="bulkupload-dropdown" (valueChange)="getDropdownValue($event)"
                                        valueField="moduleID">
                                        <ng-template kendoComboBoxItemTemplate let-company>
                                            <span class="TextTruncate"
                                                title="{{company.pageConfigAliasName==''?company.moduleName:company.pageConfigAliasName}}">
                                                {{company.pageConfigAliasName==''?company.moduleName:company.pageConfigAliasName}}
                                            </span>
                                        </ng-template>
                                    </kendo-combobox>
                                </div>
                            </div>
                            <div class="d-inline-block plr20 pt20"
                                *ngIf=" isConditionalDropDown=='common'">
                                <label for="portfoliocompany" class="Caption-M"
                                    [ngClass]="CompanyDisabled ? 'disabled-label-color' : ''">Company Names</label>
                                <div class="pt-1">
                                    <kendo-combobox  [disabled]="CompanyDisabled" [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="portfolioCompData"
                                        #company="ngModel" [fillMode]="'solid'" [filterable]="true" name="portfoliocompany" [virtual]="virtual"
                                        class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="PorfolioCompanies"
                                        [filterable]="true"  textField="companyName" [valuePrimitive]="false"
                                        placeholder="Search or select company"  id="bulkupload-companyselected" (valueChange)="CompanySelected($event)" valueField="portfolioCompanyID">
                                        <ng-template kendoComboBoxItemTemplate let-company>
                                            <span class="TextTruncate" title="{{company.companyName}}">
                                                {{company.companyName}}
                                            </span>
                                          </ng-template>
                                    </kendo-combobox>
                                </div>
                                <div *ngIf="IsValidCompany"
                                    class="text-danger bulk-pt-pl">
                                    Company name required</div>
                            </div>
                            <div class="d-inline-block plr20 pt20" *ngIf="isConditionalDropDown=='FOF'">
                                <label for="fund" class="nep-form-label">Fund
                                    Names</label>
                                <div class="pt-1">
                                    <kendo-multiselect #multiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'"
                                    [ngClass]="{'k-multiselect-search':fundList?.length>0}" [kendoDropDownFilter]="filterSettings"
                                    name="funds" [virtual]="virtualMultiSelect" [clearButton]="false"
                                    class="k-multiselect-custom k-dropdown-width-280" [tagMapper]="tagMapper"
                                    [data]="FundsList" [(ngModel)]="fundList" [textField]="'fundName'" [valueField]="'fundID'"
                                    (valueChange)="debounce(onFundChangeModel, 300)($event)"  id="bulkupload-onfundchange" [autoClose]="false" placeholder="Select Fund Name"
                                    >
                                    <ng-template kendoMultiSelectHeaderTemplate *ngIf="FundsList?.length > 0">
                                        <div class="inline-container">
                                            <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isFundCheckAll" [indeterminate]="isIndet()" id="bulkupload-selectfund" (click)="onSelectAllClick(multiSelect)" />
                                            <kendo-label for="chk">{{ toggleAllText }}</kendo-label>
                                            <kendo-label for="chk" > Select All</kendo-label>
                                        </div>
                                    </ng-template>
                                    <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                        <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                                    </ng-template>
                                  </kendo-multiselect>
                                </div>
                        </div>
                        <div class="d-inline-block plr20 pt20" *ngIf="isFundKpis">
                            <label for="fund" class="nep-form-label">Fund Name</label>
                            <div class="pt-1">
                                <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="fundData"
                                    #fund="ngModel" [fillMode]="'solid'" [filterable]="true" name="fund" [virtual]="virtual"
                                    class="k-dropdown-width-280 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                    [data]="FundsList" [filterable]="true" [value]="fundData" [valuePrimitive]="false"
                                    textField="fundName" placeholder="Select Fund Name" id="bulkupload-onfundchange" (valueChange)="onFundKpisChangeModel($event)"
                                    valueField="fundID">
                                    <ng-template kendoComboBoxItemTemplate let-fund>
                                        <span class="TextTruncate"
                                            title="{{fund.fundName}}">
                                            {{fund.fundName}}
                                        </span>
                                    </ng-template>
                                </kendo-combobox>
                            </div>
                    </div>
                            <div class="d-inline-block plr20 pt20"
                                *ngIf="isConditionalDropDown=='investor'">
                                <label for="fund" class="Caption-M">Investor Name</label>
                                <div class="pt-1">
                                    <kendo-combobox   [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="investorData"
                                    #investor="ngModel" [fillMode]="'solid'" [filterable]="true" name="investor" [virtual]="virtual"
                                    class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="investorList"
                                    [filterable]="true"  textField="investorName" [valuePrimitive]="false"
                                    placeholder="Search or select investor" (valueChange)="OnInvestorSelected($event)"  id="bulkupload-oninvestorselected" valueField="investorId">
                                    <ng-template kendoComboBoxItemTemplate let-investor>
                                        <span class="TextTruncate" title="{{investor.investorName}}">
                                            {{investor.investorName}}
                                        </span>
                                      </ng-template>
                                </kendo-combobox>
                                </div>
                                <div *ngIf="IsValidInvestor"
                                    class="text-danger p-error-msg">
                                    Investor name required</div>
                            </div>
                            <div class=" d-inline-block pr20 pt20"
                                *ngIf="strModuleType=='Adhoc' && isPeriod">
                                <label for="fund" class="Caption-M">Period Type</label>
                                <div class="pt-1">
                                    <kendo-combobox [clearButton]="false" [(ngModel)]="adhocPeriod" #periodType="ngModel" [fillMode]="'solid'" name="period"
                                        [virtual]="virtual" class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'"
                                        [data]="adhocPeriodTypes" [filterable]="true" [valuePrimitive]="false" textField="type" placeholder="Select Period"
                                        (valueChange)="validateSelectedPeriod($event)"  id="bulkupload-selectperiod" valueField="type">
                                    </kendo-combobox>
                                    <div *ngIf="isPeriodValidate"
                                        class="text-danger p-error-msg">
                                        Select Period required</div>
                                </div>
                            </div>
                            <div class="d-inline-block pr20 pt20"
                                *ngIf="(strModuleType=='Adhoc' && adhocPeriod.type!='') || strModuleType=='MonthlyReport'">
                                <label for="fund" class="Caption-M">{{adhocPeriod.type}}</label>
                                <label for="portfoliocompany" class="Caption-M" *ngIf="(strModuleType=='MonthlyReport' && portfolioCompData?.companyName != null)"
                                [ngClass]="CompanyDisabled ? 'disabled-label-color' : ''">As-of Month</label>
                                <div class="pt-1" *ngIf="adhocPeriod.type=='Monthly' || (strModuleType=='MonthlyReport' && portfolioCompData?.companyName != null)">
                                        <kendo-datepicker bottomView="year" topView="decade" calendarType="classic" 
                                        class="k-picker-custom-solid k-datepicker-height-32 k-datepicker-width-240" [format]="format" [fillMode]="'solid'"
                                        placeholder="Select Month" id="monthpicker" name="toMonth" [(ngModel)]="adhocMonth"
                                        [value]="getFormattedDate(adhocMonth)"  id="bulkupload-onSelectMonth" (valueChange)="onSelectMonth($event)"></kendo-datepicker>
                                    <div *ngIf="isMonthValidate && strModuleType!='MonthlyReport'"
                                        class="text-danger p-error-msg">
                                        Select Monthly required</div>
                                    <div *ngIf="isMonthValidate && strModuleType=='MonthlyReport'" class="text-danger p-error-msg">
                                        Select As-of Month</div>
                                </div>
                                <div class="pt-1" *ngIf="adhocPeriod.type=='Quarterly'">
                                    <quarter-year-control [ControlName]="'quarterYearValue'"
                                        [QuarterYear]="adhocQuarter" (onCalendarYearPicked)="fromQuarterYear($event)">
                                    </quarter-year-control>
                                    <div *ngIf="isQuarterValidate"
                                        class="text-danger p-error-msg">
                                        Select Quarterly required</div>
                                </div>
                                <div class="pt-1" *ngIf="adhocPeriod.type=='Yearly'">
                                    <kendo-combobox [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="adhocModelYear"
                                        #yearly="ngModel" [fillMode]="'solid'" [filterable]="true" name="yearly" [virtual]="virtual"
                                        class="k-dropdown-width-240 k-custom-solid-dropdown k-dropdown-height-32" [size]="'medium'" [data]="yearOptions"
                                        [filterable]="true" textField="text" placeholder="Select year"  id="bulkupload-selectyear" (valueChange)="yearOptionsChange($event)"
                                        valueField="value">
                                    </kendo-combobox>
                                    <div *ngIf="isYearValidate"
                                        class="text-danger p-error-msg">
                                        Select Yearly required</div>
                                </div>
                            </div>
                            <div class="d-inline-block pt20" id="template-container">
                                <div class="download-template-container pr-3" *ngIf="!isNewTemplate"
                                    (click)="showClickMessage && hideUnauthorized ? DownloadTemplate():''"  id="bulkupload-downloadtemplate"
                                    [ngClass]="showClickMessage && hideUnauthorized ? 'pointerIcon':'disableIcon'">
                                    <span class="template-icon-style-container-0">
                                        <img *ngIf="showClickMessage && hideUnauthorized" class="template-icon-style"
                                            [src]="'assets/dist/images/TemplateDownloadWhite.svg'" alt="">
                                        <img *ngIf="!showClickMessage || !hideUnauthorized"
                                            class="template-icon-style opacity-40"
                                            [src]="'assets/dist/images/TemplateDownloadGrey.svg'" alt="">
                                    </span>

                                    <span
                                        [ngClass]="showClickMessage && hideUnauthorized ? 'dtc-enabled' : 'dtc-disabled'"
                                        class="template-label-style-0" id="btn-bulk-upload-template">
                                        Template
                                    </span>
                                   
                                    <span class="pl-1 upload-loader pt-2" *ngIf="isLoading">
                                        <i aria-hidden="true"
                                            class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                                    </span>
                                </div>
                                <div class="download-template-container pr-3" *ngIf="isNewTemplate"
                                    (click)="showClickMessage && hideUnauthorized ? DownloadTemplate():''"  id="bulkupload-downloadtemplate"
                                    [ngClass]="showClickMessage && hideUnauthorized ? 'pointerIcon':'disableIcon'">
                                    <img *ngIf="showClickMessage && hideUnauthorized" class="template-icon-style template-icon-style-new"
                                        [src]="'assets/dist/images/MdDownload.svg'" alt="">
                                    <img *ngIf="!showClickMessage || !hideUnauthorized" class="template-icon-style template-icon-style-new opacity-40"
                                        [src]="'assets/dist/images/MdDownload.svg'" alt="">
                                    <span [ngClass]="showClickMessage && hideUnauthorized ? 'dtc-enabled' : 'dtc-disabled'"
                                        class="template-label-style-0" id="btn-template-download">Template Download</span>
                                    <span class="pl-1 upload-loader pt-2" *ngIf="isLoading">
                                        <i aria-hidden="true" class="download-circle-btn-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 pt20"  *ngIf="showClickMessage && hideUnauthorized">
                        <div class="title-text plr20 pb12">
                            <strong>Download excel template and bulk upload data</strong>
                        </div>
                        <div class="file-upload-section-body plr20">
                            <div>
                                <span class="ptb12">Note:-</span>
                                <ol class="plr20" *ngIf="moduleName !== 'exchange rates'">
                                    <li>Template includes sample data. Replace the same with relevant data..</li>
                                    <li>Follow the format laid out in the template. Do not modify any headers.</li>
                                    <li>File size limit is 20MB.</li>
                                    <li>For KPI's, allowed symbols are %, $ for KPI data.</li>
                                    <li *ngIf="isConditionalDropDown=='FOF'">
                                        File uploaded should be in zipped excel format.
                                    </li>
                                </ol>

                                <ol class="plr20" *ngIf="moduleName === 'exchange rates'">
                                    <li>The Template includes a Master Currency tab with the relevant currency codes.
                                        <br /> Please adhere to these codes when specifying the ‘From Currency’ and ‘To
                                        Currency’ in the template.
                                    </li>
                                    <li>The daily currency rates are to be entered as per the format laid out in the
                                        template. Do not modify the ‘From Currency’ and ‘To Currency’ headers. </li>
                                    <li>File Size limit is 20 MB.</li>
                                </ol>
                            </div>

                        </div>
                        <div class="file-upload-section-header"
                            [ngClass]="messageClass==='errorMessage' ? 'bottom-border':''">
                            <div id="buttonSection">
                                <span class="errors-title-container" *ngIf="messageClass==='errorMessage'">
                                    <img class="error-info-icon" [src]="'assets/dist/images/red_info_icon.svg'" alt="">
                                    <span class="error-title errorColor">
                                        <strong>Errors ({{errorCount}})</strong>
                                    </span>
                                </span>
                                <div class="pull-right pl12" *ngIf="!isNewTemplate" id="div-bulk-upload">
                                    <nep-button Type="Primary" [disabled]="files.length === 0 || messages.length > 0 || errormessages.length > 0"
                                        (click)="onUpload()"  id="bulkupload-onupload">
                                        Upload <span class="pl-1 upload-loader" *ngIf="loading">
                                            <i aria-hidden="true"
                                                class="download-circle-loader fa fa-circle-o-notch fa-1x fa-fw"></i>
                                        </span>
                                    </nep-button>

                                </div>
                                <div class="uploadButton pull-right d-flex" *ngIf="!isNewTemplate" id="browse-file-container">
                                    <div class="textEllipsis uploadLogoIcon" (click)="file.click()"  id="bulkupload-fileclick"
                                        title={{uploadFilePlaceholder}}>
                                        <input class="hidefile" #file (click)="file.value = null" value=""  id="bulkupload-filevalue"
                                            accept=".xlsx,.xls,.zip" (change)="onSelect($event.target.files)" type="file">
                                        <img *ngIf="browseicon" class="pull-left browseIcon" id="img-browse-file"
                                            [src]="'assets/dist/images/Desktop Grey.svg'" alt="">
                                        <span class="beatColor browseButton" id="btn-browse-file">{{uploadFilePlaceholder}}</span>
                                    </div>
                                    <div *ngIf="!browseicon" class="icon">
                                        <img *ngIf="ProgressCancel" class="pull-right"
                                            (click)="deleteiconclick(filename)"  id="bulkupload-deleteiconclick"
                                            [src]="'assets/dist/images/ClearIcon.svg'" alt="">
                                    </div>
                                </div>
                                <div class="uploadButton upload-button-all pull-right d-flex pr-2" *ngIf="isNewTemplate">
                                    <div class="textEllipsis uploadLogoIcon all-upload-browse" (click)="onclickAllUpload()"  id="bulkupload-onclickAllUpload"
                                        title="All Uploads">
                                        <img *ngIf="browseicon" class="pull-left  browseIcon"
                                            [src]="'assets/dist/images/MdUpload.svg'" alt="">
                                        <span class="beatColor  browseButton">All Uploads</span>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="error-msgs-container" *ngIf="messageClass==='errorMessage'">
                            <div *ngFor="let item of errormessages">
                                <div *ngIf="item.sheetname != null && item.sheetname != ''">
                                    <div class="ptb12 error-row sheetname"
                                        [ngClass]="item.isExpanded? 'expanded-error' : ''" (click)="isExpand(item)"  id="bulkupload-isexpand">
                                        <div class="d-inline">
                                            <span class="plr12" *ngIf="item.isExpanded"><img alt=""
                                                    [src]="'assets/dist/images/Expand-more.svg'" /></span>
                                            <span class="plr12" *ngIf="!item.isExpanded"><img alt=""
                                                    [src]="'assets/dist/images/Expand-less.svg'" /></span>
                                            <span title=""><strong>{{item.sheetname}}</strong></span>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    *ngIf="item.isExpanded || (errormessages.length == 1 && item.sheetname == '')">
                                    <div class="ptb12 error-row row" *ngFor="let subitem of item.messages"
                                        [ngClass]="errormessages.length > 0? 'sheets-error-pl' : 'error-pl'">
                                        <div class="mw  errorColor"
                                            *ngIf="subitem.cellCode != undefined && subitem.cellCode != ''" [innerHtml]="subitem.cellCode"></div>
                                        <div *ngIf="subitem.message != undefined" class="pl-4 col-8" [innerHtml]="subitem.message">
                                        </div>
                                        <div *ngIf="subitem.message == undefined" class="pl-4 col-8" [innerHtml]="subitem"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div *ngIf="showErrorPopUp" class="nep-modal nep-modal-show nep-d-bg">
        <div class="nep-modal-mask"></div>
        <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-bulk-pdt">
            <div class="nep-card-header nep-modal-title">
                <div class="d-inline"><img class="yellowInfoIcon" src="assets/dist/images/yellowInfoIcon.svg"
                        alt="" />Alert</div>
                <div class="float-right desc-icon">
                    <span (click)="closeErrorPopup()" id="bulkupload-closeerror"><i class="fa fa-times" aria-hidden="true"></i></span>
                </div>
            </div>
            <div class="nep-card-body">
                <div> {{ popUpErrorMsg }}</div>
            </div>
        </div>
    </div>
    <div *ngIf="showAllFileUploadPopUp" class="nep-modal nep-modal-show kpi-add-edit-modal nep-kpi-bg">
        <div class="nep-modal-mask"></div>
        <div class="nep-card nep-card-shadow nep-modal-panel nep-modal-default nep-all-upload-file">
            <all-popup-bulk-upload [moduleName]="model.moduleDetails.moduleName" [companyId]="PortfolioCompanyId"   (confirmButtonEvent)="onSubmitButtonEvent($event)" 
            (cancelButtonEvent)="cancelButtonEvent($event)" (toasterMessageEvent)="toasterMessageEvent($event)" [selectedMonth]="adhocSelectedMonth"></all-popup-bulk-upload>
        </div>
    </div>
</div>
