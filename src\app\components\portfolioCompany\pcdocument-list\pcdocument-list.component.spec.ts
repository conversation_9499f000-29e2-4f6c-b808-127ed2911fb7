import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { PCDocumentListComponent } from './pcdocument-list.component';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';

describe('PCDocumentListComponent', () => {
  let component: PCDocumentListComponent;
  let fixture: ComponentFixture<PCDocumentListComponent>;
  let documentServiceSpy: jasmine.SpyObj<RepositoryConfigService>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  
  const mockDocuments = [
    { id: '1', name: 'test1.pdf', size: 1024, type: 'application/pdf' },
    { id: '2', name: 'test2.docx', size: 2048, type: 'application/msword' }
  ];

  beforeEach(() => {
    documentServiceSpy = jasmine.createSpyObj('RepositoryConfigService', ['uploadDocumentstoServer', 'getDocumentList', 'deleteDocuments']);
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning']);

    // Configure mock responses
    documentServiceSpy.getDocumentList.and.returnValue(of({ 
      isSuccess: true, 
      data: mockDocuments, 
      message: 'Documents loaded successfully' 
    }));
    
    documentServiceSpy.uploadDocumentstoServer.and.returnValue(of({ 
      isSuccess: true, 
      message: 'Files uploaded successfully' 
    }));

    documentServiceSpy.deleteDocuments.and.returnValue(of({
      isSuccess: true,
      message: 'Documents deleted successfully'
    }));

    TestBed.configureTestingModule({
      declarations: [PCDocumentListComponent],
      providers: [
        { provide: RepositoryConfigService, useValue: documentServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy }
      ]
    });
    
    fixture = TestBed.createComponent(PCDocumentListComponent);
    component = fixture.componentInstance;
    component.PortfolioCompanyId = 'test-portfolio-id';
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set selected folder and load documents', fakeAsync(() => {
    const mockData = {
      folderPath: 'test/folder/path',
      isFromUnconfig: false
    };
    
    component.setSelectedFolder(mockData);
    tick();
    
    expect(component.selectedFolderPath).toBe(mockData.folderPath);
    expect(component.isAddButtonEnabled).toBeTrue();
    expect(documentServiceSpy.getDocumentList).toHaveBeenCalled();
    expect(component.documents).toEqual(mockDocuments);
  }));

  it('should show no access error', () => {
    component.canEdit = false;
    component['toastr'] = toastrServiceSpy;
    component.showNoAccessError();
    expect(toastrServiceSpy.error).toHaveBeenCalled();
  });

  it('should check permission and return false if no access', () => {
    component.canEdit = false;
    spyOn(component, 'showNoAccessError');
    const result = component.checkPermission();
    expect(result).toBeFalse();
    expect(component.showNoAccessError).toHaveBeenCalled();
  });

  it('should check permission and return true if access', () => {
    component.canEdit = true;
    const result = component.checkPermission();
    expect(result).toBeTrue();
  });

  it('should not show document options if no permission', () => {
    component.canEdit = false;
    spyOn(component, 'checkPermission').and.returnValue(false);
    component.showOptions = false;
    component.showDocumentOptions();
    expect(component.showOptions).toBeFalse();
  });

  it('should show document options and reset state if allowed', () => {
    component.canEdit = true;
    component.isAddButtonEnabled = true;
    component.uploadedFiles = [{ name: 'test.pdf', selected: true }];
    component.actualUploadedFiles = [new File(['content'], 'test.pdf')];
    component.isAnyDocumentSelected = true;
    component.showDocumentOptions();
    expect(component.showOptions).toBeTrue();
    expect(component.uploadedFiles.length).toBe(0);
    expect(component.actualUploadedFiles.length).toBe(0);
    expect(component.isAnyDocumentSelected).toBeFalse();
  });

  it('should trigger file input click', () => {
    const fileInputMock = { nativeElement: { click: jasmine.createSpy('click') } };
    component.fileInput = fileInputMock as any;
    component.triggerFileInput();
    expect(fileInputMock.nativeElement.click).toHaveBeenCalled();
  });

  it('should set isDragging true on drag over', () => {
    const event = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;
    component.isDragging = false;
    component.onDragOver(event);
    expect(component.isDragging).toBeTrue();
    expect(event.preventDefault).toHaveBeenCalled();
    expect(event.stopPropagation).toHaveBeenCalled();
  });

  it('should set isDragging false on drag leave', () => {
    const event = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;
    component.isDragging = true;
    component.onDragLeave(event);
    expect(component.isDragging).toBeFalse();
    expect(event.preventDefault).toHaveBeenCalled();
    expect(event.stopPropagation).toHaveBeenCalled();
  });

  it('should format document name by removing extension', () => {
    expect(component.formatDocumentName('file.pdf')).toBe('file');
    expect(component.formatDocumentName('file')).toBe('file');
    expect(component.formatDocumentName('')).toBe('');
  });

  it('should handle delete selected click with no permission', () => {
    spyOn(component, 'checkPermission').and.returnValue(false);
    spyOn(component, 'hideSelectionPopup');
    component.handleDeleteSelectedClick();
    expect(component.hideSelectionPopup).not.toHaveBeenCalled();
  });

  it('should handle delete selected click with permission', () => {
    spyOn(component, 'checkPermission').and.returnValue(true);
    spyOn(component, 'hideSelectionPopup');
    component.handleDeleteSelectedClick();
    expect(component.hideSelectionPopup).toHaveBeenCalled();
  });

  it('should show error if downloadDocument called with invalid document', () => {
    component['toastr'] = toastrServiceSpy;
    component.downloadDocument(null);
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Invalid document for download.');
  });

  it('should handle error in downloadDocument observable', () => {
    component['toastr'] = toastrServiceSpy;
    component.selectedFolderPath = 'folder';
    const doc = { documentId: '1', documentName: 'file.pdf' };
    documentServiceSpy.downloadDocument = jasmine.createSpy().and.returnValue({
      subscribe: (handlers: any) => { handlers.error(); }
    });
    component.downloadDocument(doc);
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Failed to download file', '', jasmine.any(Object));
  });

  it('should not enable add button when folder path is empty', () => {
    component.setSelectedFolder('');
    expect(component.isAddButtonEnabled).toBeFalse();
  });

  it('should handle file selection', () => {
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockEvent = { target: { files: [mockFile] } };
    
    component.onFileSelected(mockEvent);
    
    expect(component.actualUploadedFiles.length).toBe(1);
    expect(component.uploadedFiles.length).toBe(1);
    expect(component.uploadedFiles[0].name).toBe('test.pdf');
    expect(component.uploadedFiles[0].selected).toBeFalse();
  });

  it('should handle drag and drop of files', () => {
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockEvent = {
      preventDefault: jasmine.createSpy('preventDefault'),
      stopPropagation: jasmine.createSpy('stopPropagation'),
      dataTransfer: { files: [mockFile] }
    } as unknown as DragEvent;
    
    component.onDrop(mockEvent);
    
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
    expect(component.isDragging).toBeFalse();
    expect(component.actualUploadedFiles.length).toBe(1);
    expect(component.uploadedFiles.length).toBe(1);
    expect(component.uploadedFiles[0].name).toBe('test.pdf');
  });

  it('should toggle all document selections', () => {
    // Setup initial files
    component.uploadedFiles = [
      { name: 'test1.pdf', selected: false },
      { name: 'test2.pdf', selected: false }
    ];
    component.showOptions = true;
    
    // Select all
    component.toggleAllDocumentsSelection();
    
    expect(component.uploadedFiles[0].selected).toBeTrue();
    expect(component.uploadedFiles[1].selected).toBeTrue();
    expect(component.isAnyDocumentSelected).toBeTrue();
    
    // Deselect all
    component.toggleAllDocumentsSelection();
    
    expect(component.uploadedFiles[0].selected).toBeFalse();
    expect(component.uploadedFiles[1].selected).toBeFalse();
    expect(component.isAnyDocumentSelected).toBeFalse();
  });

  it('should test areAllDocumentsSelected method with uploaded files', () => {
    // Test with uploaded files
    component.showOptions = true;
    
    // Case 1: No files - should return false
    component.uploadedFiles = [];
    expect(component.areAllDocumentsSelected()).toBeFalse();
    
    // Case 2: Some files selected, some not - should return false
    component.uploadedFiles = [
      { name: 'test1.pdf', selected: true },
      { name: 'test2.pdf', selected: false }
    ];
    expect(component.areAllDocumentsSelected()).toBeFalse();
    
    // Case 3: All files selected - should return true
    component.uploadedFiles = [
      { name: 'test1.pdf', selected: true },
      { name: 'test2.pdf', selected: true }
    ];
    expect(component.areAllDocumentsSelected()).toBeTrue();
  });

  it('should test areAllDocumentsSelected method with documents', () => {
    // Test with documents
    component.showOptions = false;
    
    // Case 1: No documents - should return false
    component.documents = [];
    expect(component.areAllDocumentsSelected()).toBeFalse();
    
    // Case 2: Some documents selected, some not - should return false
    component.documents = [
      { documentId: '1', documentName: 'test1.pdf', selected: true },
      { documentId: '2', documentName: 'test2.pdf', selected: false }
    ];
    expect(component.areAllDocumentsSelected()).toBeFalse();
    
    // Case 3: All documents selected - should return true
    component.documents = [
      { documentId: '1', documentName: 'test1.pdf', selected: true },
      { documentId: '2', documentName: 'test2.pdf', selected: true }
    ];
    expect(component.areAllDocumentsSelected()).toBeTrue();
  });

  it('should update selection status correctly', () => {
    // Setup test documents
    component.documents = [
      { documentId: '1', documentName: 'test1.pdf', selected: true },
      { documentId: '2', documentName: 'test2.pdf', selected: false },
      { documentId: '3', documentName: 'test3.pdf', selected: true }
    ];
    
    component.updateSelectionStatus();
    
    // Check counts and IDs are correctly calculated
    expect(component.selectedDocumentCount).toBe(2);
    expect(component.selectedDocumentIds).toEqual(['1', '3']);
    expect(component.showSelectionPopup).toBeTrue();
    expect(component.isMultipleDelete).toBeTrue();
    expect(component.isDeleteDisabled).toBeFalse();
  });

  it('should handle empty selection in updateSelectionStatus', () => {
    // Setup with no selected documents
    component.documents = [
      { documentId: '1', documentName: 'test1.pdf', selected: false },
      { documentId: '2', documentName: 'test2.pdf', selected: false }
    ];
    
    component.updateSelectionStatus();
    
    expect(component.selectedDocumentCount).toBe(0);
    expect(component.selectedDocumentIds).toEqual([]);
    expect(component.showSelectionPopup).toBeFalse();
  });

  it('should show delete confirmation for a single document', () => {
    component.canEdit = true; // Set permission to true
    const mockDocument = { documentId: '1', documentName: 'test1.pdf' };
    
    component.showDeleteConfirmation(mockDocument);
    
    expect(component.documentToDelete).toEqual(mockDocument);
    expect(component.selectedDocumentIds).toEqual(['1']);
    expect(component.isMultipleDelete).toBeFalse();
    expect(component.showDeletePopup).toBeTrue();
  });

  it('should upload selected files to server', fakeAsync(() => {
    // Setup folder path and files
    component.selectedFolderPath = 'test/folder';
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    component.actualUploadedFiles = [mockFile];
    component.uploadedFiles = [{ name: 'test.pdf', selected: true }];
    
    component.saveFilesToServer();
    tick();
    
    expect(documentServiceSpy.uploadDocumentstoServer).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalledWith('Files uploaded successfully', '', jasmine.any(Object));
    expect(component.actualUploadedFiles.length).toBe(0);
    expect(component.uploadedFiles.length).toBe(0);
    expect(component.isAnyDocumentSelected).toBeFalse();
  }));

  it('should show warning when no files are selected for upload', () => {
    // Setup no selected files
    component.uploadedFiles = [{ name: 'test.pdf', selected: false }];
    
    component.saveFilesToServer();
    
    expect(documentServiceSpy.uploadDocumentstoServer).not.toHaveBeenCalled();
    expect(toastrServiceSpy.warning).toHaveBeenCalledWith('Please select at least one file to upload.');
  });

  it('should remove unselected files', () => {
    // Setup mixed selection of files
    component.uploadedFiles = [
      { name: 'test1.pdf', selected: true },
      { name: 'test2.pdf', selected: false },
      { name: 'test3.pdf', selected: true }
    ];
    component.actualUploadedFiles = [
      new File(['content1'], 'test1.pdf'),
      new File(['content2'], 'test2.pdf'),
      new File(['content3'], 'test3.pdf')
    ];
    
    component.removeUnselectedFiles();
    
    expect(component.uploadedFiles.length).toBe(2);
    expect(component.actualUploadedFiles.length).toBe(2);
    expect(component.uploadedFiles[0].name).toBe('test1.pdf');
    expect(component.uploadedFiles[1].name).toBe('test3.pdf');
  });

  it('should reset state when back action is triggered', () => {
    // Setup initial state
    component.uploadedFiles = [{ name: 'test.pdf', selected: true }];
    component.actualUploadedFiles = [new File(['content'], 'test.pdf')];
    component.isAnyDocumentSelected = true;
    
    component.handleBackAction();
    
    expect(component.uploadedFiles.length).toBe(0);
    expect(component.actualUploadedFiles.length).toBe(0);
    expect(component.showOptions).toBeFalse();
    expect(component.isAnyDocumentSelected).toBeFalse();
  });

  it('should handle document loading failure', fakeAsync(() => {
    // Mock error response
    documentServiceSpy.getDocumentList.and.returnValue(of({ 
      isSuccess: false, 
      message: 'Failed to load documents' 
    }));
    
    const mockData = {
      folderPath: 'test/folder',
      isFromUnconfig: false
    };
    component.setSelectedFolder(mockData);
    tick();
    
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Failed to load documents');
  }));

  it('should add documents when files are uploaded', () => {
    // Setup files
    spyOn(component, 'saveFilesToServer');
    component.uploadedFiles = [{ name: 'test.pdf', selected: true }];
    
    component.addDocuments();
    
    expect(component.saveFilesToServer).toHaveBeenCalled();
  });

  it('should show warning when trying to add documents without files', () => {
    component.uploadedFiles = [];
    component.addDocuments();
    
    expect(toastrServiceSpy.warning).toHaveBeenCalledWith('Please select files to upload first.');
  });

  it('should delete selected documents', fakeAsync(() => {
    component.canEdit = true;
    component.selectedFolderPath = 'test/folder';
    component.selectedDocumentIds = ['1', '2'];
    component.isMultipleDelete = true;
    
    component.deleteSelectedDocuments();
    tick();
    
    expect(documentServiceSpy.deleteDocuments).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalledWith(
      'All the selected files have been deleted successfully.',
      '',
      jasmine.any(Object)
    );
  }));

  it('should show warning when no documents are selected for deletion', () => {
    component.canEdit = true;
    component.selectedDocumentIds = [];
    
    component.deleteSelectedDocuments();
    
    expect(toastrServiceSpy.warning).toHaveBeenCalledWith('No documents selected for deletion.');
    expect(documentServiceSpy.deleteDocuments).not.toHaveBeenCalled();
  });

  it('should close delete popup and reset document to delete', () => {
    // Setup initial state
    component.showDeletePopup = true;
    component.documentToDelete = { documentId: '1', documentName: 'test1.pdf' };
    
    component.closeDeletePopup();
    
    expect(component.showDeletePopup).toBeFalse();
    expect(component.documentToDelete).toBeNull();
  });

  it('should hide selection popup and show delete popup', () => {
    // Setup initial state
    component.showSelectionPopup = true;
    component.showDeletePopup = false;
    
    component.hideSelectionPopup();
    
    expect(component.showSelectionPopup).toBeFalse();
    expect(component.showDeletePopup).toBeTrue();
  });

  it('should close selection popup and clear document selections', () => {
    // Setup initial state
    component.showSelectionPopup = true;
    component.documents = [
      { documentId: '1', documentName: 'test1.pdf', selected: true },
      { documentId: '2', documentName: 'test2.pdf', selected: true }
    ];
    
    component.closeSelectionPopup();
    
    expect(component.showSelectionPopup).toBeFalse();
    expect(component.documents[0].selected).toBeFalse();
    expect(component.documents[1].selected).toBeFalse();
    expect(component.selectedDocumentCount).toBe(0);
    expect(component.selectedDocumentIds).toEqual([]);
  });

  it('should handle successful document deletion', fakeAsync(() => {
    // Setup
    component.canEdit = true;
    component.selectedFolderPath = 'test/folder';
    component.selectedDocumentIds = ['1', '2'];
    component.showSelectionPopup = true;
    component.showDeletePopup = true;

    // Mock successful response
    documentServiceSpy.deleteDocuments.and.returnValue(of({
      isSuccess: true,
      message: 'Documents deleted successfully'
    }));
    
    spyOn(component, 'loaddocumentstoShow');
    
    component.deleteSelectedDocuments();
    tick();
    
    // Verify service was called with correct parameters
    expect(documentServiceSpy.deleteDocuments).toHaveBeenCalled();
    
    // Get the FormData argument passed to deleteDocuments
    const callArgs = documentServiceSpy.deleteDocuments.calls.mostRecent().args;
    expect(callArgs[0]).toBe(component.PortfolioCompanyId);
    expect(callArgs[1] instanceof FormData).toBeTrue();
    
    // Verify UI state was reset correctly
    expect(component.showSelectionPopup).toBeFalse();
    expect(component.showDeletePopup).toBeFalse();
    expect(component.selectedDocumentCount).toBe(0);
    expect(component.selectedDocumentIds).toEqual([]);
    expect(component.loaddocumentstoShow).toHaveBeenCalledWith('test/folder');
  }));

  it('should handle document deletion error', fakeAsync(() => {
    // Setup
    component.canEdit = true;
    component.selectedFolderPath = 'test/folder';
    component.selectedDocumentIds = ['1', '2'];
    
    // Mock error response
    documentServiceSpy.deleteDocuments.and.returnValue(of({
      isSuccess: false,
      message: 'Failed to delete documents'
    }));
    
    component.deleteSelectedDocuments();
    tick();
    
    expect(toastrServiceSpy.error).toHaveBeenCalledWith(
      'Failed to delete documents', 
      '', 
      jasmine.any(Object)
    );
  }));

  describe('downloadDocument', () => {
    let mockBlob: Blob;
    let mockDocument: any;

    beforeEach(() => {
      mockBlob = new Blob(['test content'], { type: 'application/pdf' });
      mockDocument = { documentId: '123', documentName: 'test.pdf' };
      // Add the spy for downloadDocument
      documentServiceSpy.downloadDocument = jasmine.createSpy('downloadDocument').and.returnValue(of(mockBlob));
    });

    it('should show error if document is invalid', () => {
      component.downloadDocument(null);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Invalid document for download.');
      expect(documentServiceSpy.downloadDocument).not.toHaveBeenCalled();
    });

    it('should call service and trigger download for valid document', () => {
      // Use a real anchor Node for appendChild/removeChild
      const anchor = window.document.createElement('a');
      const clickSpy = spyOn(anchor, 'click');
      // Use type assertion to satisfy the spy signature
      const appendSpy = spyOn(window.document.body, 'appendChild').and.callFake(((node: any) => node) as any);
      const removeSpy = spyOn(window.document.body, 'removeChild').and.callFake(((node: any) => node) as any);
      spyOn(window.document, 'createElement').and.returnValue(anchor);

      component.downloadDocument(mockDocument);

      expect(documentServiceSpy.downloadDocument).toHaveBeenCalledWith(
        component.PortfolioCompanyId,
        mockDocument.documentId,
        component.selectedFolderPath
      );
      expect(appendSpy).toHaveBeenCalledWith(anchor);
      expect(clickSpy).toHaveBeenCalled();
      expect(removeSpy).toHaveBeenCalledWith(anchor);
    });

    it('should show error toast on download failure', () => {
      documentServiceSpy.downloadDocument.and.returnValue({
        subscribe: (observer: any) => observer.error()
      } as any);
      component.downloadDocument(mockDocument);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Failed to download file', '', {
        positionClass: 'toast-center-center',
      });
    });
  });
});
