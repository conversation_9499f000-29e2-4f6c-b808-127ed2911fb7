﻿import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Subject, throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";

@Injectable()
export class FileUploadService {
  myAppUrl: string = "";
  message: string="";
  constructor(private _http: HttpClient, @Inject("BASE_URL") baseUrl: string, private router: Router) {
    this.myAppUrl = baseUrl;
  }

  private _subject = new Subject<any>();
  subjetcValue: any = { progress: 0, message: "" };
  statuscValue: any = { code: "", message: "" };
  changeFileUploadProgress(value: any) {
    this._subject.next(value);
  }
  get events$() {
    return this._subject.asObservable();
  }

  errorHandler(error: any) {
    return throwError(() => error);
  }

  importBulkData(formData: any, strAPIURL: string) {
    return this._http.post<any>(this.myAppUrl + strAPIURL, formData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  importFoFBulkData(formData: any) {
    return this._http.post<any>(this.myAppUrl + "api/fund-of-fund/import", formData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  importCashflowDetails(formData: any) {
    return this._http
      .post<any>(this.myAppUrl + "api/cashflow/import", formData)
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }
/**
 * exportTemplates is a method for exporting templates based on the module type.
 *
 * @param {any} model - The model object should contain the moduleType property.
 * The moduleType property is used to determine the URL for the API call.
 *
 * The method uses a urlMap object to map module types to their corresponding API endpoints.
 * If the moduleType property of the model object does not match any key in the urlMap object,
 * a default API endpoint ("api/bulk-upload/import/template") is used.
 *
 * The method makes a POST request to the determined API endpoint, passing the model object as the request body.
 * The responseType is set to "blob" and observe option is set to "response" to handle the file download.
 *
 * If the HTTP request fails, the catchError operator will call the errorHandler method.
 *
 * @returns {Observable} - The method returns an Observable. The subscribers can then handle the response accordingly.
 */
  exportTemplates(model: any) {
    const DEFAULT_URL = "api/bulk-upload/import/template";
    const DEFAULT_KPI_URL = "api/bulk-upload/Kpi/import/template";
    const MONTHLY_REPORT_URL = "api/monthlyreport/import/template";
    const urlMap = {
      "ESG": "api/esg/import/template",
      "TradingRecords": DEFAULT_KPI_URL,
      "CreditKPI": DEFAULT_KPI_URL,
      "OperationalKPI": DEFAULT_KPI_URL,
      "InvestmentKPI": DEFAULT_KPI_URL,
      "ImpactKPI": DEFAULT_KPI_URL,
      "CompanyKPI": DEFAULT_KPI_URL,
      "Financials":DEFAULT_KPI_URL,
      "CapTable":DEFAULT_KPI_URL,
      "MonthlyReport": MONTHLY_REPORT_URL,
      "FundFinancials": DEFAULT_KPI_URL,
      "FundKpis": DEFAULT_KPI_URL,
  };

  let url = this.myAppUrl + (urlMap[model.moduleType] || DEFAULT_URL);
  const moduleType = model?.moduleType?.toLowerCase();
  if (moduleType && /custom|other/.test(moduleType)) {
    url = this.myAppUrl + DEFAULT_KPI_URL;
  }
    return this._http
      .post(url, model, {
        responseType: "blob",
        observe: "response"
      })
      .pipe(catchError(this.errorHandler));
  }
  foFExportTemplates(model: any) {
    return this._http
      .post(this.myAppUrl + "api/fof/import/template", model, {
        responseType: "blob",
        observe: "response"
      })
      .pipe(catchError(this.errorHandler));
  }

  UploadESG(formData : FormData) {
    return this._http.post<any>(this.myAppUrl + 'api/esg/upload/template', formData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  OnSubmitAllFilesUpload(formData: any) {
    return this._http.post<any>(this.myAppUrl + 'api/doc-info/bulk-insert', formData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
}

