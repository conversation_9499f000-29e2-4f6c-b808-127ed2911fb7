import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailConfigurationUsersComponent } from './email-configuration-users.component';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, SimpleChange } from '@angular/core';
import { DialogService } from '@progress/kendo-angular-dialog';
import { GridModule } from '@progress/kendo-angular-grid';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { ToastrService } from 'ngx-toastr';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { of, throwError } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';

describe('EmailConfigurationUsersComponent', () => {
  let component: EmailConfigurationUsersComponent;
  let fixture: ComponentFixture<EmailConfigurationUsersComponent>;
  let dialogServiceSpy: jasmine.SpyObj<DialogService>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let repositoryConfigServiceSpy: jasmine.SpyObj<RepositoryConfigService>;

  // Mock data
  const mockCompany = { companyId: 1, name: 'Test Company' };
  const mockCategories = [
    { categoryId: 1, category: 'Category 1' },
    { categoryId: 2, category: 'Category 2' }
  ];
  const mockDocumentTypes = [
    { documentTypeID: 1, documentName: 'Document Type 1' },
    { documentTypeID: 2, documentName: 'Document Type 2' }
  ];
  const mockUserInfo = [
    {
      userInformationID: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      category: 'Category 1',
      recipient: 'To',
      documentTypes: [
        { documentTypeID: 1, documentName: 'Document Type 1' }
      ]
    }
  ];

  beforeEach(async () => {
    // Create spies
    dialogServiceSpy = jasmine.createSpyObj('DialogService', ['open']);
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']); repositoryConfigServiceSpy = jasmine.createSpyObj('RepositoryConfigService', [
      'getUserInformationByCompany',
      'getCategoriesAndDocumentTypes',
      'createUserInformation',
      'getUserInformationById',
      'updateUserInformation'
    ]);

    // Setup default spy returns
    repositoryConfigServiceSpy.getUserInformationByCompany.and.returnValue(of(mockUserInfo));
    repositoryConfigServiceSpy.getCategoriesAndDocumentTypes.and.returnValue(of({
      categories: mockCategories,
      documentTypes: mockDocumentTypes
    }));
    repositoryConfigServiceSpy.createUserInformation.and.returnValue(of({ userInformationId: { isSuccess: true, message: 'User information added successfully' } }));

    await TestBed.configureTestingModule({
      declarations: [EmailConfigurationUsersComponent],
      imports: [
        GridModule,
        ButtonsModule,
        FormsModule,
        DropDownsModule
      ],
      providers: [
        { provide: DialogService, useValue: dialogServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: RepositoryConfigService, useValue: repositoryConfigServiceSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EmailConfigurationUsersComponent);
    component = fixture.componentInstance;
  });

  // Test Case 1: Component Creation
  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  // Test Case 2: Initial Display State
  it('should display "Please Select Company" when no companies are selected', () => {
    component.selectedCompanies = [];
    fixture.detectChanges();

    const noCompanyContainer = fixture.nativeElement.querySelector('.email-user-container');
    const userInfoWrapper = fixture.nativeElement.querySelector('.email-user-wrapper');

    expect(noCompanyContainer).toBeTruthy();
    expect(userInfoWrapper).toBeFalsy();
    expect(noCompanyContainer.textContent).toContain('Please Select Company');
  });

  // Test Case 3: Display User Info Grid
  it('should display user info grid when companies are selected', () => {
    component.selectedCompanies = [mockCompany];
    fixture.detectChanges();

    const userInfoWrapper = fixture.nativeElement.querySelector('.email-user-wrapper');
    const gridElement = fixture.nativeElement.querySelector('kendo-grid');

    expect(userInfoWrapper).toBeTruthy();
    expect(gridElement).toBeTruthy();
  });

  // Test Case 4: Loading User Information
  it('should load user information when companies are selected', () => {
    component.selectedCompanies = [mockCompany];
    component.ngOnInit();

    expect(repositoryConfigServiceSpy.getUserInformationByCompany).toHaveBeenCalledWith(mockCompany.companyId);
    expect(component.userInfoItems.length).toBe(1);
    expect(component.userInfoItems[0].employeeName).toBe('John Doe');
    expect(component.userInfoItems[0].email).toBe('<EMAIL>');
  });

  // Test Case 5: Handle Selected Companies Change
  it('should reload user information when selected companies change', () => {
    const changes = {
      selectedCompanies: new SimpleChange(null, [mockCompany], true)
    };

    component.selectedCompanies = [mockCompany];
    component.ngOnChanges(changes);

    expect(repositoryConfigServiceSpy.getUserInformationByCompany).toHaveBeenCalledWith(mockCompany.companyId);
  });

  // Test Case 6: Add User Overlay
  it('should open add user overlay and load categories and document types', () => {
    component.selectedCompanies = [mockCompany];
    component.showAddUserOverlay();

    expect(component.showAddUser).toBeTrue();
    expect(repositoryConfigServiceSpy.getCategoriesAndDocumentTypes).toHaveBeenCalledWith(mockCompany.companyId);
    expect(component.categories).toEqual(mockCategories);
    expect(component.documentTypes).toEqual(mockDocumentTypes);
  });

  // Test Case 7: Form Validation
  it('should properly validate email format', () => {
    // Setup form data with invalid email
    component.selectedCompanies = [mockCompany];
    component.showAddUserOverlay();
    component.selectedCategory = mockCategories[0];
    component.selectedDocTypes = [mockDocumentTypes[0]];
    component.selectedRecipient = { text: 'To', value: 1 };
    component.newUserInfo.email = 'invalid-email';

    // Check validation result
    component.checkFormValidity();
    expect(component.formValid).toBeFalse();

    // Now with valid email
    component.newUserInfo.email = '<EMAIL>';
    component.checkFormValidity();
    expect(component.formValid).toBeTrue();
  });

  // Test Case 8: Adding New User Info
  it('should successfully add new user information', () => {
    component.selectedCompanies = [mockCompany];
    component.showAddUser = true;
    component.selectedCategory = mockCategories[0];
    component.selectedDocTypes = [mockDocumentTypes[0]];
    component.selectedRecipient = { text: 'To', value: 1 };
    component.newUserInfo = {
      employeeName: 'Jane Smith',
      email: '<EMAIL>',
      category: '',
      documentType: '',
      recipientType: ''
    };
    component.formValid = true;

    // Call the method
    component.addNewUserInfo();

    // Verify the API was called with correct data
    expect(repositoryConfigServiceSpy.createUserInformation).toHaveBeenCalledWith({
      entityId: mockCompany.companyId,
      name: 'Jane Smith',
      email: '<EMAIL>',
      categoryId: mockCategories[0].categoryId,
      recipient: 'To',
      documentTypeIds: [mockDocumentTypes[0].documentTypeID]
    });

    // Verify success message was shown
    expect(toastrServiceSpy.success).toHaveBeenCalled();
  });

  // Test Case 9: Error Handling
  it('should handle API errors when loading user information', () => {
    repositoryConfigServiceSpy.getUserInformationByCompany.and.returnValue(
      throwError(() => new Error('API Error'))
    );

    component.selectedCompanies = [mockCompany];
    component.ngOnInit();

    expect(component.isLoading).toBeFalse();
  });

  // Test Case 10: Edit User Info - Load User Information
  it('should load user information for editing', (done) => {
    // Setup mock for getUserInformationById
    const mockUserInfoDetail = {
      userInformationID: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      category: 'Category 1',
      recipient: 'To',
      documentTypes: [
        { documentTypeID: 1, documentName: 'Document Type 1' }
      ]
    };
    repositoryConfigServiceSpy.getUserInformationById = jasmine.createSpy().and.returnValue(of(mockUserInfoDetail));
    repositoryConfigServiceSpy.getCategoriesAndDocumentTypes = jasmine.createSpy().and.returnValue(of({
      categories: mockCategories,
      documentTypes: mockDocumentTypes
    }));
    component.selectedCompanies = [mockCompany];
    component.categories = mockCategories;
    component.documentTypes = mockDocumentTypes;
    component.recipientTypes = [
      { text: 'To', value: 1 },
      { text: 'CC', value: 2 }
    ];
    const userItem = {
      id: 1,
      employeeName: 'John Doe',
      category: 'Category 1',
      documentType: 'Document Type 1',
      recipientType: 'To',
      email: '<EMAIL>'
    };
    // Patch the method to call getUserInformationById after categories/docTypes loaded
    spyOn(component, 'loadCategoriesAndDocumentTypes').and.callFake(() => {
      component.categories = mockCategories;
      component.documentTypes = mockDocumentTypes;
      // Simulate async
      return Promise.resolve().then(() => {
        repositoryConfigServiceSpy.getUserInformationById(userItem.id).subscribe((result) => {
          expect(result).toEqual(mockUserInfoDetail);
          done();
        });
      });
    });
    component.editUserInfo(userItem);
    expect(component.isEditMode).toBeTrue();
    expect(component.currentUserInfoId).toBe(1);
    expect(component.showAddUser).toBeTrue();
  });

  // Test Case 11: Edit User Info - Handle API errors
  it('should handle API errors when loading user information for editing', (done) => {
    repositoryConfigServiceSpy.getUserInformationById = jasmine.createSpy().and.returnValue(
      throwError(() => new Error('API Error'))
    );
    repositoryConfigServiceSpy.getCategoriesAndDocumentTypes = jasmine.createSpy().and.returnValue(of({
      categories: mockCategories,
      documentTypes: mockDocumentTypes
    }));
    component.selectedCompanies = [mockCompany];
    const userItem = {
      id: 1,
      employeeName: 'John Doe',
      category: 'Category 1',
      documentType: 'Document Type 1',
      recipientType: 'To',
      email: '<EMAIL>'
    };
    spyOn(component, 'loadCategoriesAndDocumentTypes').and.callFake(() => {
      component.categories = mockCategories;
      component.documentTypes = mockDocumentTypes;
      return Promise.resolve().then(() => {
        repositoryConfigServiceSpy.getUserInformationById(userItem.id).subscribe({
          error: (err) => {
            expect(err).toBeTruthy();
            done();
          }
        });
      });
    });
    component.editUserInfo(userItem);
  });

  // Test Case 13: Update User Info - Form Validation Error
  it('should validate form before updating user information', () => {
    component.formValid = false;
    component.isEditMode = true;
    // Instead of spying on private method, just call updateUserInfo and check that API is not called
    component.updateUserInfo();
    expect(repositoryConfigServiceSpy.updateUserInformation).not.toHaveBeenCalled();
  });

 describe('deleteUserInfo', () => {
    it('should open the delete confirmation popup and set selectedUserInfo', () => {
      const userItem = {
        id: 1,
        employeeName: 'John Doe',
        category: 'Category 1',
        documentType: 'Document Type 1',
        recipientType: 'To',
        email: '<EMAIL>'
      };
      component.deleteUserInfo(userItem);
      expect(component.selectedUserInfo).toEqual(userItem);
      expect(component.showDeletePopup).toBeTrue();
    });

    it('should call deleteUserInformation and reload user info on confirm (success)', () => {
      const userItem = {
        id: 1,
        employeeName: 'John Doe',
        category: 'Category 1',
        documentType: 'Document Type 1',
        recipientType: 'To',
        email: '<EMAIL>'
      };
      component.selectedUserInfo = userItem;
      component.showDeletePopup = true;
      const mockResponse = { isSuccess: true, message: 'User deleted successfully' };
      repositoryConfigServiceSpy.deleteUserInformation = jasmine.createSpy().and.returnValue(of(mockResponse));
      const loadUserInformationSpy = spyOn<any>(component, 'loadUserInformation').and.callFake(() => {});
      component.triggerDeleteAction();
      expect(repositoryConfigServiceSpy.deleteUserInformation).toHaveBeenCalledWith(userItem.id);
      expect(loadUserInformationSpy).toHaveBeenCalled();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('User deleted successfully', '', { positionClass: 'custom-toast' });
      expect(component.isLoading).toBeFalse();
      expect(component.showDeletePopup).toBeFalse();
    });

    it('should show error toast if delete API returns error (isSuccess false)', () => {
      const userItem = {
        id: 1,
        employeeName: 'John Doe',
        category: 'Category 1',
        documentType: 'Document Type 1',
        recipientType: 'To',
        email: '<EMAIL>'
      };
      component.selectedUserInfo = userItem;
      component.showDeletePopup = true;
      const mockResponse = { isSuccess: false, message: 'Delete failed' };
      repositoryConfigServiceSpy.deleteUserInformation = jasmine.createSpy().and.returnValue(of(mockResponse));
      const loadUserInformationSpy = spyOn<any>(component, 'loadUserInformation').and.callFake(() => {});
      component.triggerDeleteAction();
      expect(repositoryConfigServiceSpy.deleteUserInformation).toHaveBeenCalledWith(userItem.id);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Delete failed', '', { positionClass: 'custom-toast' });
      expect(component.isLoading).toBeFalse();
      expect(component.showDeletePopup).toBeFalse();
    });

    it('should show error toast if delete API throws error', () => {
      const userItem = {
        id: 1,
        employeeName: 'John Doe',
        category: 'Category 1',
        documentType: 'Document Type 1',
        recipientType: 'To',
        email: '<EMAIL>'
      };
      component.selectedUserInfo = userItem;
      component.showDeletePopup = true;
      const errorResponse = { error: { message: 'API Error' } };
      repositoryConfigServiceSpy.deleteUserInformation = jasmine.createSpy().and.returnValue(throwError(() => errorResponse));
      const loadUserInformationSpy = spyOn<any>(component, 'loadUserInformation').and.callFake(() => {});
      component.triggerDeleteAction();
      expect(repositoryConfigServiceSpy.deleteUserInformation).toHaveBeenCalledWith(userItem.id);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('API Error', '', { positionClass: 'custom-toast' });
      expect(component.isLoading).toBeFalse();
      expect(component.showDeletePopup).toBeFalse();
    });

    it('should close the delete popup and clear selectedUserInfo on cancelDelete', () => {
      component.selectedUserInfo = { id: 1 } as any;
      component.showDeletePopup = true;
      component.cancelDelete();
      expect(component.selectedUserInfo).toBeNull();
      expect(component.showDeletePopup).toBeFalse();
    });
  });

  describe('isDocumentTypeCheckAll logic', () => {
    beforeEach(() => {
      // Setup documentTypes
      component.documentTypes = [
        { documentTypeID: 1, documentName: 'Doc1' },
        { documentTypeID: 2, documentName: 'Doc2' }
      ];
    });

    it('should set isDocumentTypeCheckAll to true if selectedDocTypes length equals documentTypes length', () => {
      component.selectedDocTypes = [
        { documentTypeID: 1, documentName: 'Doc1' },
        { documentTypeID: 2, documentName: 'Doc2' }
      ];
      component.onDocumentTypeSelectionChange(component.selectedDocTypes);
      expect(component.isDocumentTypeCheckAll).toBeTrue();
    });

    it('should set isDocumentTypeCheckAll to false if selectedDocTypes length does not equal documentTypes length', () => {
      component.selectedDocTypes = [
        { documentTypeID: 1, documentName: 'Doc1' }
      ];
      component.onDocumentTypeSelectionChange(component.selectedDocTypes);
      expect(component.isDocumentTypeCheckAll).toBeFalse();
    });
  });
});