import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailReminderDetailsComponent } from './email-reminder-details.component';
import { UpdateEmailRemainderDto } from '../../model/config-model';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrService } from 'ngx-toastr';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RepositoryConfigService } from '../../../../services/repository.config.service';

describe('EmailReminderDetailsComponent', () => {
  let component: EmailReminderDetailsComponent;
  let fixture: ComponentFixture<EmailReminderDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [ EmailReminderDetailsComponent ],
      providers: [
        { provide: ToastrService, useValue: { success: () => {}, error: () => {} } },
        { provide: 'BASE_URL', useValue: 'http://localhost' }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    fixture = TestBed.createComponent(EmailReminderDetailsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should identify group recipients correctly', () => {
    const emailRecipient = { internalID: 1, name: 'John', emailAddress: '<EMAIL>' };
    const groupRecipient = { internalID: 2, name: '', emailAddress: 'Admin Group' };

    expect(component.isGroupRecipient(emailRecipient)).toBeFalsy();
    expect(component.isGroupRecipient(groupRecipient)).toBeTruthy();
  });

  it('should return correct display text for recipients', () => {
    const emailRecipient = { internalID: 1, name: 'John', emailAddress: '<EMAIL>' };
    const groupRecipient = { internalID: 2, name: '', emailAddress: 'Admin Group' };

    expect(component.getRecipientDisplayText(emailRecipient)).toBe('<EMAIL>');
    expect(component.getRecipientDisplayText(groupRecipient)).toBe('Admin Group');
  });

  it('should load reminder data when reminderDetails is provided', () => {
    const mockReminderDetails = {
      toRecipients: [ { internalID: 1, name: 'John Doe', emailAddress: '<EMAIL>' } ],
      ccRecipients: [ { internalID: 2, name: '', emailAddress: 'Admin Group' } ],
      subject: 'Test Subject',
      messageBody: 'Test Email Body',
      totalNumberOfReminders: 3,
      totalRemindersSent: 1,
      totalErrorAndSkippedReminders: 0,
      lastReminderSentDate: '2025-06-09T10:00:00Z',
      nextReminderScheduledDate: '2025-06-10T10:00:00Z'
    };
    component.reminderDetails = mockReminderDetails as any;
    component.ngOnInit();
    expect(component.subject).toBe('Test Subject');
    expect(component.emailBody).toBe('Test Email Body');
    expect(component.totalReminders).toBe(3);
    expect(component.toRecipients.length).toBe(1);
    expect(component.ccRecipients.length).toBe(1);
    expect(component.lastReminderDate).toContain('09 Jun 2025');
    expect(component.nextReminderDate).toContain('10 Jun 2025');
  });

  it('should handle skip cycle with missing reminderId', () => {
    spyOn(component['toastrService'], 'error');
    component.reminderId = '';
    component.onSkipCycleChange(true);
    expect(component['toastrService'].error).toHaveBeenCalledWith('Reminder ID is required to skip cycle', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBe(false);
  });

  it('should call skipReminderCycle and update state on success', () => {
    const repoService = TestBed.inject<any>(RepositoryConfigService);
    const toastr = component['toastrService'];
    spyOn(repoService, 'skipReminderCycle').and.returnValue({ subscribe: (handlers: any) => handlers.next({ message: 'Skipped!' }) });
    spyOn(component as any, 'updateNextCycleRemainderStats');
    spyOn(toastr, 'success');
    component.reminderId = '123';
    component.onSkipCycleChange(true);
    expect(toastr.success).toHaveBeenCalledWith('Skipped!', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBe(true);
    expect((component as any).updateNextCycleRemainderStats).toHaveBeenCalledWith('123');
  });

  it('should call skipReminderCycle and handle error', () => {
    const repoService = TestBed.inject<any>(RepositoryConfigService);
    const toastr = component['toastrService'];
    spyOn(repoService, 'skipReminderCycle').and.returnValue({ subscribe: (handlers: any) => handlers.error({ message: 'Failed!' }) });
    spyOn(toastr, 'error');
    component.reminderId = '123';
    component.onSkipCycleChange(true);
    expect(toastr.error).toHaveBeenCalledWith('Failed!', '', { positionClass: 'toast-center-center' });
    expect(component.isSkipCycleEnabled).toBe(false);
  });
});
