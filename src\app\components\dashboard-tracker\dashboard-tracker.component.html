<app-loader-component *ngIf="isLoading"></app-loader-component>


<div class="kendo-container">
  <kendo-grid id="dashboardTrackerTable" class="dashboard-tracker-table" [ngClass]="passedClass" [data]="gridData"
    [scrollable]="'scrollable'" [sortable]="true" [style.min-width.px]="gridColumns.length * 200 + 400">

    <ng-container *ngFor="let col of gridColumns; let i = index">
      <!-- Special handling for Portfolio Company column (with logo and name) -->
      <kendo-grid-column *ngIf="col.name === 'Portfolio Company Name'" [name]="col.name" [sticky]="true" [title]="col.name" [width]="300">
        <ng-template *ngIf="isDashboardConfigurationTab" kendoGridHeaderTemplate>
          <input type="checkbox" kendoCheckBox />
          <span class="Body-R header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
            <div class="d-flex align-items-center">
              <ng-container *ngIf="dataItem.CompanyLogo && dataItem.CompanyLogo.trim() !== ''">
                <img [src]="dataItem.CompanyLogo" alt="logo" class="company-logo mr-2 p-1" />                
              </ng-container>
              <ng-container *ngIf="!dataItem.CompanyLogo || dataItem.CompanyLogo.trim() === ''">
                <span class="text-logo mr-2">{{ dataItem['Portfolio Company Name']?.slice(0,1) }}</span>
              </ng-container>
              <span>{{ dataItem['Portfolio Company Name'] }}</span>
            </div>
        </ng-template>
      </kendo-grid-column>

      <!-- All other columns -->
      <kendo-grid-column *ngIf="col.name !== 'Portfolio Company Name' && col.name !== 'CompanyLogo'" [name]="col.name"
        [title]="col.name" [width]="200">
        <ng-template *ngIf="isDashboardConfigurationTab" kendoGridHeaderTemplate>
          <input type="checkbox" kendoCheckBox />
          <span class="Body-R header-title ml-3">{{ col.name }}</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <!-- Dropdown for unique identifier columns with comma separated values -->
          <ng-container *ngIf="col.isDropDown && dataItem[col.name] && isDashboardConfigurationTab">
            <div class="dropdown-input">
              <!-- Kendo ComboBox Dropdown -->
              <kendo-combobox id="dropdown-input-{{ i }}" class="k-custom-solid-dropdown k-dropdown-height-32"
                [data]="dataItem[col.name].split(',')" [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'"
                placeholder="Select Here">
              </kendo-combobox>
            </div>
          </ng-container>
          <!-- Empty cell if value is empty -->
          <ng-container *ngIf="!col.isDropDown">
            {{ dataItem[col.name] || '' }}
          </ng-container>
        </ng-template>
      </kendo-grid-column>
    </ng-container>

    <!-- Add new column at the end for plus icon button -->
    <kendo-grid-column title="" [width]="60" [sticky]="true" *ngIf="isDashboardConfigurationTab">
      <ng-template kendoGridHeaderTemplate>
        <app-kendo-button (click)="navigateToDashboardConfig()" name="dashboard-tracker-setting" type="Secondary"
          icon="plus" aria-label="Add dashboard configuration">
        </app-kendo-button>
      </ng-template>
    </kendo-grid-column>

    <!-- default screen if grid data is empty -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="text-center py-5 mt-5" *ngIf="gridData.length === 0">
        <img src="assets/dist/images/Illustrations.svg" alt="No data" class="mb-3" />
        <p class="mb-0 Body-R content-secondary">No Data Found</p>
      </div>
    </ng-template>
  </kendo-grid>
</div>