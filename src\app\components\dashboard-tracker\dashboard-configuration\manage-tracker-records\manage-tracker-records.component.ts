import { Component, Input, OnChanges, OnInit, QueryList, SimpleChanges, ViewChildren } from '@angular/core';
import {ExpansionPanelComponent} from "@progress/kendo-angular-layout";
import { chevronUpIcon, SVGIcon } from "@progress/kendo-svg-icons";
import { DashboardTrackerService } from 'src/app/services/dashboard-tracker.service';
import { DashboardConfigurationConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-manage-tracker-records',
  templateUrl: './manage-tracker-records.component.html',
  styleUrls: ['./manage-tracker-records.component.scss']
})
export class ManageTrackerRecordsComponent implements OnInit, OnChanges {
  @Input() isNewColumnAdded: boolean = false;
  @ViewChildren(ExpansionPanelComponent)
  panels: QueryList<ExpansionPanelComponent>;
  arrowUpIcon: SVGIcon = chevronUpIcon;
  showDropdownValues: number = 2;
  dropdownValueMaxLength: number = 10;
  data: any[] = [];
  constructor(private readonly dashboardTrackerService: DashboardTrackerService) { }
  ngOnChanges(changes: SimpleChanges): void {
     if (changes['isNewColumnAdded'].currentValue) {
        this.UpdateColumnsCollection();
     }
  }
  
ngOnInit(): void {
  this.UpdateColumnsCollection();
}
  private UpdateColumnsCollection() {
    this.dashboardTrackerService.getDashboardTrackerColumnData().subscribe({
      next: (res) => {
        this.data = res.map((item: any) => ({
          fieldType: item.fieldTypeName,
          dataType: item.dataTypeName,
          namePattern: item.name,
          createdDate: item.createdOn ? this.formatDate(item.createdOn) : '',
         // mapTo: DashboardConfigurationConstants.maptoFields.find(i => i.value === item.mapTo).text,
          dropdownList: item.dropdownList ?? undefined,
          isEnable: item.isActive
        }));
      },
      error: (err) => {
        console.error('Error fetching column data', err);
      }
    });
  }

private formatDate(dateString: string): string {
  const date = new Date(dateString);
  return !dateString || isNaN(date.getTime())
    ? ''
    : `${date.getDate().toString().padStart(2, '0')}/${date.toLocaleString('en-US', { month: 'short' })}/${date.getFullYear().toString().slice(-2)}`;
}
  onToggleChange(event: any, item: any): void {
    item.isEnable = event;
  }

  onToggleClick(event: any, item: any): void {
    event.stopPropagation();
  }

  public onAction(index: number): void {
    this.panels.forEach((panel, idx) => {
      if (idx !== index && panel.expanded) {
        panel.toggle();
      }
    });
  }
}
