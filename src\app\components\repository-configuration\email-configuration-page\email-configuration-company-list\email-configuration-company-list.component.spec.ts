import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EmailConfigurationCompanyListComponent } from './email-configuration-company-list.component';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { EventEmitter } from '@angular/core';

describe('EmailConfigurationCompanyListComponent', () => {
  let component: EmailConfigurationCompanyListComponent;
  let fixture: ComponentFixture<EmailConfigurationCompanyListComponent>;
  let repositoryConfigServiceSpy: jasmine.SpyObj<RepositoryConfigService>;

  // Mock data
  const mockCompanies = [
    { companyName: 'Company A', portfolioCompanyID: 1 },
    { companyName: 'Company B', portfolioCompanyID: 2 },
    { companyName: 'Company C', portfolioCompanyID: 3 }
  ];

  beforeEach(async () => {
    // Create spy for repository config service
    const repoConfigSpy = jasmine.createSpyObj('RepositoryConfigService', 
      ['getPortfolioCompanies'], 
      { resetInProgress$: of(false) }
    );
    
    // Configure spy to return mock data
    repoConfigSpy.getPortfolioCompanies.and.returnValue(of(mockCompanies));

    await TestBed.configureTestingModule({
      declarations: [EmailConfigurationCompanyListComponent],
      imports: [FormsModule, HttpClientTestingModule],
      providers: [
        { provide: RepositoryConfigService, useValue: repoConfigSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA] // For app-loader-component
    }).compileComponents();

    repositoryConfigServiceSpy = TestBed.inject(RepositoryConfigService) as jasmine.SpyObj<RepositoryConfigService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EmailConfigurationCompanyListComponent);
    component = fixture.componentInstance;
    
    // The key change: Replace the EventEmitter with our own that we can spy on
    component.selectedCompanies = new EventEmitter<any[]>();
    spyOn(component.selectedCompanies, 'emit').and.callThrough();
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load companies on init', () => {
    expect(repositoryConfigServiceSpy.getPortfolioCompanies).toHaveBeenCalled();
    expect(component.companies.length).toBe(3);
    expect(component.companies[0].name).toBe('Company A');
    expect(component.companies[0].companyId).toBe(1);
    expect(component.companies[0].selected).toBeFalse();
  });

  it('should filter companies based on search term', () => {
    // Set search term
    component.searchTerm = 'Company A';
    
    // Check filtered results
    expect(component.filteredCompanies.length).toBe(1);
    expect(component.filteredCompanies[0].name).toBe('Company A');
  });

  it('should update searchedCompanies when search term changes', () => {
    const event = { target: { value: 'Company A' } } as unknown as Event;
    
    component.onInputChange(event);
    
    expect(component.searchTerm).toBe('company a');
    expect(component.searchedCompanies.length).toBe(1);
    expect(component.searchedCompanies[0].name).toBe('Company A');
  });
});